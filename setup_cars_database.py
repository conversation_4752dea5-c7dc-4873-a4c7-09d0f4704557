#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a local SQLite database with car information.
This creates a simple example database with car names, makes, and models.
"""

import sqlite3
import os

def create_cars_database():
    """Create a SQLite database with a cars table and sample data."""
    
    # Database file path
    db_path = "cars_database.db"
    
    # Remove existing database if it exists
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"Removed existing database: {db_path}")
    
    # Connect to SQLite database (creates file if it doesn't exist)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create the cars table
    cursor.execute('''
        CREATE TABLE cars (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            make TEXT NOT NULL,
            model TEXT NOT NULL,
            year INTEGER
        )
    ''')
    
    # Sample car data
    cars_data = [
        ("Toyota Camry", "Toyota", "Camry", 2023),
        ("Honda Civic", "Honda", "Civic", 2022),
        ("Ford Mustang", "Ford", "Mustang", 2023),
        ("BMW 3 Series", "BMW", "3 Series", 2022),
        ("Mercedes C-Class", "Mercedes-Benz", "C-Class", 2023),
        ("Audi A4", "Audi", "A4", 2022),
        ("Chevrolet Corvette", "Chevrolet", "Corvette", 2023),
        ("Tesla Model 3", "Tesla", "Model 3", 2022),
        ("Volkswagen Golf", "Volkswagen", "Golf", 2023),
        ("Subaru Outback", "Subaru", "Outback", 2022),
        ("Mazda CX-5", "Mazda", "CX-5", 2023),
        ("Nissan Altima", "Nissan", "Altima", 2022),
        ("Hyundai Elantra", "Hyundai", "Elantra", 2023),
        ("Kia Sorento", "Kia", "Sorento", 2022),
        ("Jeep Wrangler", "Jeep", "Wrangler", 2023),
        ("Ram 1500", "Ram", "1500", 2022),
        ("GMC Sierra", "GMC", "Sierra", 2023),
        ("Cadillac Escalade", "Cadillac", "Escalade", 2022),
        ("Lexus RX", "Lexus", "RX", 2023),
        ("Acura TLX", "Acura", "TLX", 2022)
    ]
    
    # Insert sample data
    cursor.executemany('''
        INSERT INTO cars (name, make, model, year)
        VALUES (?, ?, ?, ?)
    ''', cars_data)
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print(f"✅ Database created successfully: {db_path}")
    print(f"📊 Inserted {len(cars_data)} cars into the database")
    
    return db_path

def query_database_examples(db_path):
    """Show some example queries on the cars database."""
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("\n🔍 Example queries:")
    print("=" * 50)
    
    # Query 1: All cars
    print("\n1. All cars:")
    cursor.execute("SELECT * FROM cars LIMIT 5")
    results = cursor.fetchall()
    for row in results:
        print(f"   ID: {row[0]}, Name: {row[1]}, Make: {row[2]}, Model: {row[3]}, Year: {row[4]}")
    print("   ... (showing first 5 rows)")
    
    # Query 2: Cars by make
    print("\n2. Toyota cars:")
    cursor.execute("SELECT name, model, year FROM cars WHERE make = 'Toyota'")
    results = cursor.fetchall()
    for row in results:
        print(f"   {row[0]} ({row[1]}, {row[2]})")
    
    # Query 3: Count by make
    print("\n3. Count of cars by make:")
    cursor.execute("SELECT make, COUNT(*) as count FROM cars GROUP BY make ORDER BY count DESC")
    results = cursor.fetchall()
    for row in results:
        print(f"   {row[0]}: {row[1]} cars")
    
    # Query 4: Cars from 2023
    print("\n4. Cars from 2023:")
    cursor.execute("SELECT name FROM cars WHERE year = 2023")
    results = cursor.fetchall()
    for row in results:
        print(f"   {row[0]}")
    
    conn.close()

if __name__ == "__main__":
    print("🚗 Setting up Cars Database")
    print("=" * 30)
    
    db_path = create_cars_database()
    query_database_examples(db_path)
    
    print(f"\n💡 To interact with the database:")
    print(f"   sqlite3 {db_path}")
    print(f"   Or use any SQLite client/tool")
    print(f"\n📋 Table schema:")
    print(f"   CREATE TABLE cars (")
    print(f"       id INTEGER PRIMARY KEY AUTOINCREMENT,")
    print(f"       name TEXT NOT NULL,")
    print(f"       make TEXT NOT NULL,")
    print(f"       model TEXT NOT NULL,")
    print(f"       year INTEGER")
    print(f"   )")
